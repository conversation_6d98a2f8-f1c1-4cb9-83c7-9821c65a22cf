const express = require('express')
const cors = require('cors')
const { OpenAI } = require('openai')
require('dotenv').config()

const app = express()
app.use(cors())
app.use(express.json())

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })

app.post('/api/logo-prompt', async (req, res) => {
  const {
    nombre, descripcion, colores,
    publicoObjetivo, eslogan, valores,
    sector, competencia, usos,
  } = req.body

  const prompt = `
Actúa como un diseñador gráfico senior con más de 10 años de experiencia en diseño de logotipos.
Crea una propuesta conceptual de logotipo para la siguiente marca, considerando tendencias actuales, psicología del color, simetría, legibilidad y adaptabilidad.

Información proporcionada:
- Nombre: ${nombre}
- Descripción: ${descripcion}
- Colores deseados: ${colores}
${publicoObjetivo ? `- Público objetivo: ${publicoObjetivo}` : ''}
${eslogan ? `- Eslogan: ${eslogan}` : ''}
${valores ? `- Valores clave: ${valores}` : ''}
${sector ? `- Sector/Industria: ${sector}` : ''}
${competencia ? `- Marcas similares o competencia: ${competencia}` : ''}
${usos ? `- Usos previstos del logo: ${usos}` : ''}

Elabora una propuesta clara, profesional y que enfatice cómo la simplicidad puede aportar fuerza, versatilidad y memorabilidad al diseño.`

  try {
    const completion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      model: 'gpt-4'
    })
    res.json({ resultado: completion.choices[0].message.content })
  } catch (err) {
    console.error(err)
    res.status(500).json({ error: 'Error al contactar con OpenAI.' })
  }
})

app.listen(3000, () => {
  console.log('Servidor escuchando en http://localhost:3000')
})
