import { useState } from "react";
import axios from "axios";

const FormPromptLogo = () => {
  const [form, setForm] = useState({
    nombre: "",
    descripcion: "",
    colores: "",
    publicoObjetivo: "",
    eslogan: "",
    valores: "",
    sector: "",
    competencia: "",
    usos: "",
  });
  const [respuesta, setRespuesta] = useState("");

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const { data } = await axios.post(
        "http://localhost:3000/api/logo-prompt",
        form
      );
      // Assert the expected type of data
      setRespuesta((data as { resultado: string }).resultado);
    } catch (err) {
      setRespuesta("Error al generar la propuesta.");
    }
  };

  return (
    <div className="max-w-3xl mx-auto p-4 space-y-4">
      <h1 className="text-2xl font-bold">Generador de Brief para Logotipo</h1>
      <form onSubmit={handleSubmit} className="space-y-3">
        {[
          "nombre",
          "descripcion",
          "colores",
          "publicoObjetivo",
          "eslogan",
          "valores",
          "sector",
          "competencia",
          "usos",
        ].map((field) => (
          <input
            key={field}
            name={field}
            placeholder={field}
            onChange={handleChange}
            className="w-full p-2 border"
          />
        ))}
        <button type="submit" className="bg-blue-600 text-white px-4 py-2">
          Generar propuesta
        </button>
      </form>
      {respuesta && (
        <pre className="mt-6 bg-gray-100 p-4 rounded whitespace-pre-wrap">
          {respuesta}
        </pre>
      )}
    </div>
  );
};
export default FormPromptLogo;
